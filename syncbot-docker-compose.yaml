services:
  mt5:
    image: flamintdao/mt5:latest
    container_name: mt5
    volumes:
      - ${VOLUME_MT5_CONFIG_PATH}:/config
      - ${VOLUME_SCRIPT_PATH}:/scripts
    ports:
      - 3011:3000
      - 8001:8001
    environment:
      - TZ=Asia/Saigon
      - CUSTOM_USER=minhdc
      - PASSWORD=saobachkim
    restart: unless-stopped
    networks:
      - kamal

  rabbitmq:
    image: rabbitmq
    container_name: rabbitmq
    environment:
      - TZ=${TZ}
    ports:
      - 5672:5672
    restart: unless-stopped
    networks:
      - kamal

  celery_worker:
    image: flamintdao/synctradepython
    container_name: celery_worker
    depends_on:
      - rabbitmq
    volumes:
      - ${VOLUME_SCRIPT_PATH}:/scripts
    entrypoint: celery -A tasks worker --loglevel=info --concurrency=1
    environment:
      - CONNECTION_STRING=${INTERNAL_CONNECTION_STRING}
      - INTERNAL_API_AUTH_KEY=${INTERNAL_API_AUTH_KEY}
      - API_BASE_URL=${API_BASE_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - TZ=${TZ}
      - PYTHONUNBUFFERED=1
      - MT5_URL=${MT5_URL_DEDAULT}
    restart: unless-stopped
    networks:
      - kamal

  script_manager:
    image: flamintdao/synctradepython
    container_name: script_manager
    depends_on:
      - rabbitmq
    ports:
      - 8000:8000
    volumes:
      - ${VOLUME_SCRIPT_PATH}:/scripts
    entrypoint: /scripts/entrypoint.sh
    environment:
      - CONNECTION_STRING=${INTERNAL_CONNECTION_STRING}
      - INTERNAL_API_AUTH_KEY=${INTERNAL_API_AUTH_KEY}
      - API_BASE_URL=${API_BASE_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - TZ=${TZ}
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - kamal

  # syncbot:
  #   image: flamintdao/synctradepython
  #   container_name: syncbot
  #   restart: unless-stopped
  #   depends_on:
  #     - mt5
  #     - celery_worker
  #   volumes:
  #     - scripts:/scripts
  #   entrypoint: sh -c "python3 /scripts/main.py || [ $? -eq 1 ]"
  #   env_file:
  #     - ./scripts/live.env
  #   healthcheck:
  #     test: ["CMD-SHELL", "pidof python3 || exit 1"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 1m

networks:
  kamal:
    external: true

volumes:
  scripts:
    external: true
  mt5config:
    external: true
